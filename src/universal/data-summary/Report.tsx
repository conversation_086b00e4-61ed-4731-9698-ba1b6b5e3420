import { SyncOutlined } from '@ant-design/icons'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { But<PERSON>, <PERSON><PERSON>, <PERSON>confirm, Result } from 'antd'
import { useState } from 'react'

import { type APIResponse, request } from '@/lib/request.ts'

export const ReportModal = ({ selectedKeys }: { selectedKeys: string[] }) => {
  const queryClient = useQueryClient()

  const [isModalOpen, setIsModalOpen] = useState(false)

  const [reportStatus, setReportStatus] = useState<
    'loading' | 'error' | 'success'
  >('success')
  const [reportMessage, setReportMessage] = useState('')

  const STATUS_CARD = {
    loading: (
      <Result
        title="上报中..."
        icon={<SyncOutlined spin />}
        extra={
          <Button
            onClick={() => {
              queryClient.cancelQueries({ queryKey: ['report'] })
              setIsModalOpen(false)
            }}
          >
            取消
          </Button>
        }
      />
    ),
    error: (
      <Result
        title="上报失败"
        status="error"
        subTitle={'报错: ' + reportMessage}
        extra={
          <Button onClick={() => setIsModalOpen(false)} type="primary">
            确定
          </Button>
        }
      />
    ),
    success: (
      <Result
        title="上报成功"
        status="success"
        extra={
          <Button onClick={() => setIsModalOpen(false)} type="primary">
            确定
          </Button>
        }
      />
    ),
  }

  const handleReport = useMutation({
    mutationKey: ['report'],
    mutationFn: async () => {
      setReportStatus('loading')

      const res = await request<APIResponse<null>>('/approval/pending-mul', {
        method: 'POST',
        body: { node_ids: selectedKeys },
      })

      if (res.code !== 200001) {
        setReportStatus('error')
        setReportMessage(res.message)
        return
      }
      setReportStatus('success')
    },

    onError: (err) => {
      setReportStatus('error')
      setReportMessage(JSON.stringify(err))
    },
    onSuccess() {
      setReportStatus('success')
    },
  })

  return (
    <>
      <Popconfirm
        title="确认上报所选项？"
        okText="确认"
        cancelText="取消"
        onConfirm={() => {
          setIsModalOpen(true)
          handleReport.mutate()
        }}
      >
        <Button disabled={selectedKeys.length < 1}>数据上报</Button>
      </Popconfirm>

      <Modal
        title="数据上报"
        width={420}
        height={270}
        open={isModalOpen}
        onCancel={() => setIsModalOpen(false)}
        onOk={() => setIsModalOpen(false)}
        footer={null}
      >
        {STATUS_CARD[reportStatus]}
      </Modal>
    </>
  )
}
