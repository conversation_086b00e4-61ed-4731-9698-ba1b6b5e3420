import { createFileRoute, Navigate } from '@tanstack/react-router'

export const Route = createFileRoute('/_auth/')({
  component: RouteComponent,
  staticData: {
    menu: false,
    title: '首页',
  },
  // beforeLoad: () => {
  //   const token = getToken()
  //   if (!token) {
  //     window.location.href = getLoginURL()
  //   }
  // },
})

function RouteComponent() {
  return <Navigate to="/basic-report/fixed-assets" />
}
