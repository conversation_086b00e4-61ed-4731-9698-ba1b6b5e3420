'use client'

import { useQuery } from '@tanstack/react-query'
import { createFileRoute, useNavigate } from '@tanstack/react-router'
import { <PERSON><PERSON>, <PERSON> } from 'antd'
import { motion, AnimatePresence, type Variants } from 'framer-motion'
import {
  CheckCircle,
  AlertCircle,
  Loader2,
  Refresh<PERSON><PERSON>,
  Sparkles,
} from 'lucide-react'
import { useEffect } from 'react'

import { getLoginURL, setToken } from '@/lib/auth'
import { request, type APIResponse } from '@/lib/request'

export const Route = createFileRoute('/authentication/callback')({
  component: RouteComponent,
})

// 动画变体 - 修复类型错误
const containerVariants: Variants = {
  hidden: {
    opacity: 0,
    scale: 0.8,
  },
  visible: {
    opacity: 1,
    scale: 1,
    transition: {
      duration: 0.5,
      ease: 'easeOut',
    },
  },
  exit: {
    opacity: 0,
    scale: 0.8,
    transition: {
      duration: 0.3,
    },
  },
}

const iconVariants: Variants = {
  hidden: {
    scale: 0,
    rotate: -180,
  },
  visible: {
    scale: 1,
    rotate: 0,
    transition: {
      type: 'spring',
      stiffness: 200,
      damping: 15,
    },
  },
}

const pulseVariants: Variants = {
  pulse: {
    scale: [1, 1.2, 1],
    opacity: [0.5, 0.8, 0.5],
    transition: {
      duration: 2,
      repeat: Number.POSITIVE_INFINITY,
      ease: 'easeInOut',
    },
  },
}

const bounceVariants: Variants = {
  bounce: {
    y: [0, -10, 0],
    transition: {
      duration: 0.6,
      repeat: Number.POSITIVE_INFINITY,
      ease: 'easeInOut',
    },
  },
}

const sparkleVariants: Variants = {
  sparkle: {
    scale: [0, 1, 0],
    rotate: [0, 180, 360],
    transition: {
      duration: 1.5,
      repeat: Number.POSITIVE_INFINITY,
      ease: 'easeInOut',
    },
  },
}

function RouteComponent() {
  const searchParams = new URLSearchParams(window.location.search)
  const navigate = useNavigate()
  const code = searchParams.get('code')
  const state = searchParams.get('state')

  const { data, error, isLoading } = useQuery({
    queryKey: ['oauth', code, state],
    queryFn: async () => {
      const response = await request<
        APIResponse<{
          token: string
        }>
      >('/uc-login', {
        method: 'post',
        body: {
          code: code!,
        },
      })
      if (response.code !== 200001) {
        throw new Error(response.message.error_description)
      }
      return response
    },
    enabled: !!code,
    staleTime: 0,
    retry: false,
  })

  useEffect(() => {
    if (data?.code !== 200001) return
    if (!data?.data?.token) return
    setToken(data.data.token)
    const timeout = setTimeout(() => {
      navigate({ to: decodeURIComponent(state || '/') })
    }, 1500)

    return () => clearTimeout(timeout)
  }, [data, navigate, state])

  return (
    <div className="relative flex min-h-screen items-center justify-center overflow-hidden p-4">
      {/* 背景动画 */}
      <motion.div
        className="absolute inset-0 bg-gradient-to-br from-blue-50 via-white to-indigo-50"
        animate={{
          background: [
            'linear-gradient(135deg, #dbeafe 0%, #ffffff 50%, #e0e7ff 100%)',
            'linear-gradient(135deg, #e0e7ff 0%, #f8fafc 50%, #dbeafe 100%)',
            'linear-gradient(135deg, #dbeafe 0%, #ffffff 50%, #e0e7ff 100%)',
          ],
        }}
        transition={{
          duration: 8,
          repeat: Number.POSITIVE_INFINITY,
          ease: 'easeInOut',
        }}
      />

      {/* 浮动装饰元素 */}
      <motion.div
        className="absolute top-20 left-20 h-4 w-4 rounded-full bg-blue-300 opacity-30"
        animate={{
          y: [0, -20, 0],
          x: [0, 10, 0],
        }}
        transition={{
          duration: 4,
          repeat: Number.POSITIVE_INFINITY,
          ease: 'easeInOut',
        }}
      />
      <motion.div
        className="absolute right-32 bottom-32 h-6 w-6 rounded-full bg-indigo-300 opacity-20"
        animate={{
          y: [0, 15, 0],
          x: [0, -15, 0],
        }}
        transition={{
          duration: 5,
          repeat: Number.POSITIVE_INFINITY,
          ease: 'easeInOut',
          delay: 1,
        }}
      />

      <AnimatePresence mode="wait">
        {isLoading && (
          <motion.div
            key="loading"
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            className="relative z-10"
          >
            <Card className="w-full max-w-md border-0 bg-white/90 shadow-2xl backdrop-blur-md">
              <div className="p-8 text-center">
                <motion.div className="mb-6">
                  <motion.div
                    className="relative mx-auto mb-4 h-20 w-20"
                    variants={iconVariants}
                    initial="hidden"
                    animate="visible"
                  >
                    <motion.div
                      className="absolute inset-0 rounded-full bg-blue-100"
                      variants={pulseVariants}
                      animate="pulse"
                    />
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{
                        duration: 2,
                        repeat: Number.POSITIVE_INFINITY,
                        ease: 'linear',
                      }}
                      className="relative z-10"
                    >
                      <Loader2 className="h-20 w-20 text-blue-600" />
                    </motion.div>
                  </motion.div>
                </motion.div>

                <motion.h2
                  className="mb-3 text-2xl font-bold text-gray-900"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.2 }}
                >
                  正在登录中
                </motion.h2>

                <motion.p
                  className="mb-6 text-gray-600"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3 }}
                >
                  请稍候，我们正在验证您的身份...
                </motion.p>

                <motion.div
                  className="flex justify-center space-x-2"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.4 }}
                >
                  {[0, 1, 2].map((index) => (
                    <motion.div
                      key={index}
                      className="h-3 w-3 rounded-full bg-blue-600"
                      animate={{
                        scale: [1, 1.5, 1],
                        opacity: [0.5, 1, 0.5],
                      }}
                      transition={{
                        duration: 1.5,
                        repeat: Number.POSITIVE_INFINITY,
                        delay: index * 0.2,
                      }}
                    />
                  ))}
                </motion.div>
              </div>
            </Card>
          </motion.div>
        )}

        {error && (
          <motion.div
            key="error"
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            className="relative z-10"
          >
            <motion.div
              className="absolute inset-0 rounded-xl bg-gradient-to-br from-red-50 via-white to-pink-50"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
            />

            <Card className="relative w-full max-w-md border-0 bg-white/90 shadow-2xl backdrop-blur-md">
              <div className="p-8 text-center">
                <motion.div className="mb-6">
                  <motion.div
                    className="relative mx-auto mb-4 h-20 w-20"
                    variants={iconVariants}
                    initial="hidden"
                    animate="visible"
                  >
                    <motion.div
                      className="absolute inset-0 rounded-full bg-red-100"
                      animate={{
                        scale: [1, 1.1, 1],
                        opacity: [0.3, 0.6, 0.3],
                      }}
                      transition={{
                        duration: 2,
                        repeat: Number.POSITIVE_INFINITY,
                      }}
                    />
                    <motion.div
                      animate={{
                        rotate: [0, -10, 10, -10, 0],
                      }}
                      transition={{
                        duration: 0.5,
                        repeat: 3,
                        delay: 0.5,
                      }}
                    >
                      <AlertCircle className="relative z-10 h-20 w-20 text-red-500" />
                    </motion.div>
                  </motion.div>
                </motion.div>

                <motion.h2
                  className="mb-3 text-2xl font-bold text-gray-900"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.2 }}
                >
                  登录失败
                </motion.h2>

                <motion.p
                  className="mb-4 text-gray-600"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3 }}
                >
                  很抱歉，登录过程中出现了问题
                </motion.p>

                <motion.div
                  className="mb-6 rounded-lg border border-red-200 bg-red-50 p-4"
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.4 }}
                >
                  <p className="font-mono text-sm break-all text-red-700">
                    {error.message}
                  </p>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.5 }}
                >
                  <Button
                    onClick={() => {
                      window.location.href = getLoginURL('/')
                    }}
                    className="w-full bg-red-600 py-3 font-medium text-white transition-all duration-200 hover:bg-red-700"
                  >
                    <motion.div
                      className="flex items-center justify-center"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <RefreshCw className="mr-2 h-4 w-4" />
                      重新登录
                    </motion.div>
                  </Button>
                </motion.div>

                <motion.p
                  className="mt-4 text-xs text-gray-500"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.6 }}
                >
                  如果问题持续存在，请联系技术支持
                </motion.p>
              </div>
            </Card>
          </motion.div>
        )}

        {!isLoading && !error && (
          <motion.div
            key="success"
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            className="relative z-10"
          >
            <motion.div
              className="absolute inset-0 rounded-xl bg-gradient-to-br from-green-50 via-white to-emerald-50"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
            />

            <Card className="relative w-full max-w-md overflow-hidden border-0 bg-white/90 shadow-2xl backdrop-blur-md">
              {/* 庆祝粒子效果 */}
              {[...Array(6)].map((_, i) => (
                <motion.div
                  key={i}
                  className="absolute h-2 w-2 rounded-full bg-green-400"
                  initial={{
                    x: '50%',
                    y: '50%',
                    scale: 0,
                  }}
                  animate={{
                    x: `${50 + (Math.random() - 0.5) * 200}%`,
                    y: `${50 + (Math.random() - 0.5) * 200}%`,
                    scale: [0, 1, 0],
                    opacity: [0, 1, 0],
                  }}
                  transition={{
                    duration: 2,
                    delay: i * 0.1,
                    ease: 'easeOut',
                  }}
                />
              ))}

              <div className="p-8 text-center">
                <motion.div className="mb-6">
                  <motion.div
                    className="relative mx-auto mb-4 h-20 w-20"
                    variants={iconVariants}
                    initial="hidden"
                    animate="visible"
                  >
                    <motion.div
                      className="absolute inset-0 rounded-full bg-green-100"
                      animate={{
                        scale: [1, 1.2, 1],
                        opacity: [0.3, 0.6, 0.3],
                      }}
                      transition={{
                        duration: 2,
                        repeat: Number.POSITIVE_INFINITY,
                      }}
                    />
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{
                        type: 'spring',
                        stiffness: 200,
                        damping: 10,
                        delay: 0.2,
                      }}
                    >
                      <CheckCircle className="relative z-10 h-20 w-20 text-green-500" />
                    </motion.div>

                    {/* 闪烁装饰 */}
                    <motion.div
                      className="absolute -top-2 -right-2"
                      variants={sparkleVariants}
                      animate="sparkle"
                    >
                      <Sparkles className="h-6 w-6 text-yellow-400" />
                    </motion.div>
                  </motion.div>
                </motion.div>

                <motion.h2
                  className="mb-3 text-2xl font-bold text-gray-900"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3 }}
                >
                  登录成功！
                </motion.h2>

                <motion.p
                  className="mb-6 text-gray-600"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.4 }}
                >
                  正在跳转到您的目标页面...
                </motion.p>

                <motion.div
                  className="flex justify-center space-x-2"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.5 }}
                >
                  {[0, 1, 2].map((index) => (
                    <motion.div
                      key={index}
                      className="h-3 w-3 rounded-full bg-green-600"
                      variants={bounceVariants}
                      animate="bounce"
                      transition={{
                        delay: index * 0.1,
                      }}
                    />
                  ))}
                </motion.div>
              </div>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
