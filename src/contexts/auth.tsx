import { useQuery } from '@tanstack/react-query'
import * as React from 'react'

import { getLogoutURL, removeToken } from '@/lib/auth'
import { request, type APIResponse } from '@/lib/request'

export interface UserProfile {
  id: string
  username: string
  avatar: string
  role: string[]
  mobile: string
  company: string
  company_id: string
}

export interface AuthContext {
  logout: () => Promise<void>
  user: UserProfile | null
}

const AuthContext = React.createContext<AuthContext | null>(null)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const { data: user } = useQuery<UserProfile | null>({
    queryKey: ['get', '/user-info'],
    queryFn: async ({ queryKey: [, url] }) => {
      const response = await request<APIResponse<UserProfile>>(url as string)
      if (response.code !== 200001) return null
      return response.data
    },
    staleTime: 0,
    retry: false,
  })

  const logout = React.useCallback(async () => {
    removeToken()
    window.location.href = getLogoutURL()
  }, [])
  return (
    <AuthContext.Provider value={{ user: user ?? null, logout }}>
      {children}
    </AuthContext.Provider>
  )
}

// eslint-disable-next-line react-refresh/only-export-components
export function useAuth() {
  const context = React.useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
