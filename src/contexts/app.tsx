import type { Dayjs } from 'dayjs'
import dayjs from 'dayjs'
import { createContext, useContext, useState, type ReactNode } from 'react'

interface AppContextType {
  date: Dayjs
  setDate: (date: Dayjs) => void
}

const AppContext = createContext<AppContextType | undefined>(undefined)

export function AppProvider({ children }: { children: ReactNode }) {
  const [date, setDate] = useState<Dayjs>(dayjs())

  const value: AppContextType = {
    date,
    setDate,
  }
  return <AppContext.Provider value={value}>{children}</AppContext.Provider>
}

// eslint-disable-next-line react-refresh/only-export-components
export function useApp(): AppContextType {
  const context = useContext(AppContext)
  if (!context) {
    throw new Error('useApp must be used within an AppProvider')
  }
  return context
}
