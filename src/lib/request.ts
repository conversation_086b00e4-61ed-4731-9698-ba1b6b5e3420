import { ofetch } from 'ofetch'
import { stringify } from 'qs'

import { API_BASE_URL } from '@/config'

import { getToken, removeToken } from './auth'

export interface APIResponse<T> {
  code: number
  data: T
  message: {
    error: string
    error_description: string
  } & string
}

export const request = ofetch.create({
  baseURL: API_BASE_URL + '/api/v1',

  async onRequest({ options }) {
    const token = getToken()
    if (token) {
      options.headers = new Headers(options.headers)
      options.headers.set('Authorization', token)
    }
    if (options.query) {
      const searchParams = new URLSearchParams(
        stringify(options.query, { indices: true }),
      )
      options.query = Object.fromEntries(searchParams)
    }
  },

  async onResponseError({ response }) {
    if (response.status === 401) {
      removeToken()
      // window.location.href = getLoginURL()
      throw new Error('Unauthorized - redirecting to login')
    }
  },
})
