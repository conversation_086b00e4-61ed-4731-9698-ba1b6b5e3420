import { Link, useMatches } from '@tanstack/react-router'
import { Avatar, Breadcrumb, Divider, Dropdown } from 'antd'
import type { ClassValue } from 'clsx'
import { BellIcon, LogsIcon, UserIcon } from 'lucide-react'
import { useMemo } from 'react'

import { useAuth } from '@/contexts/auth'
import { getLogoutURL } from '@/lib/auth'
import { cn } from '@/lib/utils'

import { Button } from './ui/button'

export function Header({
  className,
  collapsed,
  setCollapsed,
}: {
  className?: ClassValue
  collapsed: boolean
  setCollapsed: (collapsed: boolean) => void
}) {
  const { user } = useAuth()
  const matches = useMatches()

  const breadcrumbItems = useMemo(() => {
    const hasNames = matches.filter(
      (match): match is typeof match & { loaderData: { name: string } } =>
        !!match.loaderData && 'name' in match.loaderData,
    )

    return hasNames.map((match, index, array) => ({
      title:
        index === array.length - 1 ? (
          match.loaderData.name
        ) : (
          <Link to={match.pathname}>{match.loaderData.name}</Link>
        ),
    }))
  }, [matches])

  return (
    <header className="fixed top-0 right-0 left-0 z-50 flex h-[48px] items-center border-b border-[#EAEAEA] bg-white/60 backdrop-blur-sm">
      <div
        className={cn(
          'w-[240px] shrink-0 grow-0 px-5 transition-[width] duration-300 ease-[cubic-bezier(0.2,0,0,1)]',
          {
            'w-[79px]': collapsed,
          },
          className,
        )}
      >
        <Link to="/">
          <img
            src={collapsed ? '/logo.svg' : '/nav-logo.svg'}
            className="h-[36px] w-auto"
          />
        </Link>
      </div>
      <div className="flex flex-1 items-center px-5 pl-5">
        <div className="grow- flex shrink-0 items-center">
          <Button
            className="size-7"
            variant="ghost"
            size="icon"
            onClick={() => setCollapsed(!collapsed)}
          >
            <LogsIcon className="size-4 text-[#999]" />
          </Button>
          <Breadcrumb items={breadcrumbItems} />
        </div>
        <div className="flex flex-1 items-center justify-end">
          <Button className="size-7" variant="ghost" size="icon">
            <BellIcon className="size-4 text-[#999]" />
          </Button>
          <Divider type="vertical" />
          <Dropdown
            trigger={['click']}
            menu={{
              items: [
                {
                  key: '1',
                  label: <a href={getLogoutURL()}>退出登录</a>,
                },
              ],
            }}
          >
            <div className="ml-1 flex cursor-pointer items-center gap-2">
              <Avatar
                src={user?.avatar}
                className="!size-5 !bg-[#CCDDFF]"
                icon={<UserIcon className="size-4" />}
              />
              <span className="text-xs text-[#666666]">{user?.username}</span>
            </div>
          </Dropdown>
        </div>
      </div>
    </header>
  )
}
