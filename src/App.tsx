import { QueryClientProvider } from '@tanstack/react-query'
import { RouterProvider } from '@tanstack/react-router'
import { ConfigProvider } from 'antd'
import zhCN from 'antd/locale/zh_CN'
import 'dayjs/locale/zh-cn'
import { NuqsAdapter } from 'nuqs/adapters/react'

import { queryClient } from '@/lib/query'
import { router } from '@/lib/router'

import { FormRequiredMark } from './components/FormRequiredMark'
import { ANTD_THEME_CONFIG } from './config'

export function App() {
  return (
    <ConfigProvider
      locale={zhCN}
      theme={ANTD_THEME_CONFIG}
      form={{ requiredMark: FormRequiredMark, colon: false }}
    >
      <QueryClientProvider client={queryClient}>
        <NuqsAdapter>
          <RouterProvider router={router} />
        </NuqsAdapter>
      </QueryClientProvider>
    </ConfigProvider>
  )
}
